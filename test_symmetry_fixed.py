"""
测试修正后的单跨简支梁桥在正反向地震动下的响应对称性
"""
import numpy as np
import matplotlib.pyplot as plt
import openseespy.opensees as ops
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.analyzer import BridgeAnalyzer

plt.rcParams.update({'font.family': 'SimHei'})

def test_seismic_symmetry_fixed():
    """测试修正后的地震动正反向输入的响应对称性"""
    
    # 创建简单的正弦波地震动
    dt = 0.01
    duration = 5.0
    time = np.arange(0, duration, dt)
    frequency = 2.0  # Hz
    amplitude = 0.1  # g
    
    # 正向地震动
    ground_motion_positive = amplitude * np.sin(2 * np.pi * frequency * time)
    
    # 反向地震动（取负值）
    ground_motion_negative = -amplitude * np.sin(2 * np.pi * frequency * time)
    
    # 分析参数
    pga = amplitude
    
    results = {}
    
    for direction_name, ground_motion in [("正向", ground_motion_positive), ("反向", ground_motion_negative)]:
        print(f"\n=== 分析{direction_name}地震动 ===")
        
        # 创建新的模型（每次分析都需要重新创建）
        params = BridgeParams("E:/Codes/opensees/徐汇区桥梁群/data/configs/bridge-4-龙川北路桥.json")
        model = SimplySupportedBeamModel(params)
        
        # 检查桥台约束
        print("桥台节点约束情况:")
        for i, abutment_node in enumerate(model.abutments['nodes']):
            print(f"  桥台节点 {abutment_node} ({'左' if i==0 else '右'})")
        
        # 创建分析器
        analyzer = BridgeAnalyzer(model)
        analyzer.direction = 1  # X方向（纵向）
        
        # 执行模态分析
        analyzer.modal()
        
        # 执行动力分析
        try:
            stats = analyzer.dynamic(h=ground_motion, pga=pga, dt=dt)
            
            # 读取桥台响应数据
            abutment_responses = read_abutment_responses()
            
            # 读取支座响应数据
            bearing_responses = read_bearing_responses()
            
            results[direction_name] = {
                'abutments': abutment_responses,
                'bearings': bearing_responses,
                'ground_motion': ground_motion
            }
            
            print(f"{direction_name}地震动分析完成")
            
        except Exception as e:
            print(f"{direction_name}地震动分析失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 比较结果
    if len(results) == 2:
        compare_symmetry_results(results, dt)
    
    return results


def read_abutment_responses():
    """读取桥台响应数据"""
    abutment_data = {}
    
    # 查找桥台位移文件
    import glob
    abutment_files = glob.glob('results/abutment_disp_*.txt')
    
    for file_path in abutment_files:
        try:
            data = np.loadtxt(file_path)
            if data.ndim == 1:
                data = data.reshape(-1, 1)
            
            # 从文件名提取桥台信息
            filename = file_path.split('/')[-1]
            if 'left' in filename:
                abutment_key = 'left'
            elif 'right' in filename:
                abutment_key = 'right'
            else:
                continue
                
            abutment_data[abutment_key] = {
                'time': data[:, 0],
                'disp_x': data[:, 1] if data.shape[1] > 1 else data[:, 0],
                'disp_y': data[:, 2] if data.shape[1] > 2 else np.zeros_like(data[:, 0]),
                'disp_z': data[:, 3] if data.shape[1] > 3 else np.zeros_like(data[:, 0])
            }
        except Exception as e:
            print(f"读取桥台文件 {file_path} 失败: {e}")
    
    return abutment_data


def read_bearing_responses():
    """读取支座响应数据"""
    bearing_data = {}
    
    # 查找支座变形文件
    import glob
    bearing_files = glob.glob('results/bearing_deform_*.txt')
    
    for file_path in bearing_files[:4]:  # 只读取前4个支座
        try:
            data = np.loadtxt(file_path)
            if data.ndim == 1:
                data = data.reshape(-1, 1)
            
            # 从文件名提取支座信息
            filename = file_path.split('/')[-1]
            bearing_key = filename.replace('bearing_deform_', '').replace('.txt', '')
                
            bearing_data[bearing_key] = {
                'time': data[:, 0],
                'deform_x': data[:, 1] if data.shape[1] > 1 else data[:, 0],
                'deform_y': data[:, 2] if data.shape[1] > 2 else np.zeros_like(data[:, 0]),
                'deform_z': data[:, 3] if data.shape[1] > 3 else np.zeros_like(data[:, 0])
            }
        except Exception as e:
            print(f"读取支座文件 {file_path} 失败: {e}")
    
    return bearing_data


def compare_symmetry_results(results, dt):
    """比较正反向地震动的响应结果"""
    print("\n=== 响应对称性分析 ===")
    
    # 创建图形
    fig, axes = plt.subplots(4, 1, figsize=(12, 12))
    
    # 绘制地震动
    time = np.arange(len(results['正向']['ground_motion'])) * dt
    axes[0].plot(time, results['正向']['ground_motion'], 'b-', label='正向地震动', linewidth=1)
    axes[0].plot(time, results['反向']['ground_motion'], 'r--', label='反向地震动', linewidth=1)
    axes[0].set_ylabel('加速度 (g)')
    axes[0].set_title('输入地震动')
    axes[0].legend()
    axes[0].grid(True)
    
    # 比较桥台响应
    if 'abutments' in results['正向'] and 'abutments' in results['反向']:
        pos_abutments = results['正向']['abutments']
        neg_abutments = results['反向']['abutments']
        
        # 左桥台
        if 'left' in pos_abutments and 'left' in neg_abutments:
            pos_data = pos_abutments['left']
            neg_data = neg_abutments['left']
            
            min_len = min(len(pos_data['disp_x']), len(neg_data['disp_x']))
            
            axes[1].plot(pos_data['time'][:min_len], pos_data['disp_x'][:min_len]*1000, 
                        'b-', label='左桥台 正向', linewidth=1)
            axes[1].plot(neg_data['time'][:min_len], neg_data['disp_x'][:min_len]*1000, 
                        'r--', label='左桥台 反向', linewidth=1)
            axes[1].set_ylabel('位移 (mm)')
            axes[1].set_title('左桥台响应')
            axes[1].legend()
            axes[1].grid(True)
            
            # 计算最大位移
            max_pos = np.max(np.abs(pos_data['disp_x'][:min_len]))
            max_neg = np.max(np.abs(neg_data['disp_x'][:min_len]))
            
            print(f"左桥台:")
            print(f"  正向最大位移: {max_pos*1000:.2f} mm")
            print(f"  反向最大位移: {max_neg*1000:.2f} mm")
            print(f"  比值: {max_pos/max_neg:.3f}" if max_neg > 0 else "  反向位移为0")
        
        # 右桥台
        if 'right' in pos_abutments and 'right' in neg_abutments:
            pos_data = pos_abutments['right']
            neg_data = neg_abutments['right']
            
            min_len = min(len(pos_data['disp_x']), len(neg_data['disp_x']))
            
            axes[2].plot(pos_data['time'][:min_len], pos_data['disp_x'][:min_len]*1000, 
                        'b-', label='右桥台 正向', linewidth=1)
            axes[2].plot(neg_data['time'][:min_len], neg_data['disp_x'][:min_len]*1000, 
                        'r--', label='右桥台 反向', linewidth=1)
            axes[2].set_ylabel('位移 (mm)')
            axes[2].set_title('右桥台响应')
            axes[2].legend()
            axes[2].grid(True)
            
            # 计算最大位移
            max_pos = np.max(np.abs(pos_data['disp_x'][:min_len]))
            max_neg = np.max(np.abs(neg_data['disp_x'][:min_len]))
            
            print(f"右桥台:")
            print(f"  正向最大位移: {max_pos*1000:.2f} mm")
            print(f"  反向最大位移: {max_neg*1000:.2f} mm")
            print(f"  比值: {max_pos/max_neg:.3f}" if max_neg > 0 else "  反向位移为0")
    
    # 比较支座响应
    if 'bearings' in results['正向'] and 'bearings' in results['反向']:
        pos_bearings = results['正向']['bearings']
        neg_bearings = results['反向']['bearings']
        
        # 选择第一个支座进行比较
        if pos_bearings and neg_bearings:
            bearing_key = list(pos_bearings.keys())[0]
            if bearing_key in neg_bearings:
                pos_data = pos_bearings[bearing_key]
                neg_data = neg_bearings[bearing_key]
                
                min_len = min(len(pos_data['deform_x']), len(neg_data['deform_x']))
                
                axes[3].plot(pos_data['time'][:min_len], pos_data['deform_x'][:min_len]*1000, 
                            'b-', label=f'支座 正向', linewidth=1)
                axes[3].plot(neg_data['time'][:min_len], neg_data['deform_x'][:min_len]*1000, 
                            'r--', label=f'支座 反向', linewidth=1)
                axes[3].set_ylabel('变形 (mm)')
                axes[3].set_title(f'支座 {bearing_key} 响应')
                axes[3].legend()
                axes[3].grid(True)
                
                # 计算最大变形
                max_pos = np.max(np.abs(pos_data['deform_x'][:min_len]))
                max_neg = np.max(np.abs(neg_data['deform_x'][:min_len]))
                
                print(f"支座 {bearing_key}:")
                print(f"  正向最大变形: {max_pos*1000:.2f} mm")
                print(f"  反向最大变形: {max_neg*1000:.2f} mm")
                print(f"  比值: {max_pos/max_neg:.3f}" if max_neg > 0 else "  反向变形为0")
    
    axes[-1].set_xlabel('时间 (s)')
    plt.tight_layout()
    plt.savefig('symmetry_test_fixed.png', dpi=150, bbox_inches='tight')
    plt.show()


if __name__ == "__main__":
    # 测试修正后的地震响应对称性
    results = test_seismic_symmetry_fixed()
